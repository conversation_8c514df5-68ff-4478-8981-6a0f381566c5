<?php

namespace Modules\ChatBot\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageStatus;

class ChatEmbeddingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public int $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $messageId,
        public string $question,
        public int $conversationId,
        public int $botId,
        public array $metadata = []
    ) {
        // Configure queue settings from config
        $this->onQueue(config('chatbot.async.default_queue', 'ai-processing'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('ChatEmbeddingJob started', [
                'message_id' => $this->messageId,
                'conversation_id' => $this->conversationId,
                'bot_id' => $this->botId,
            ]);

            $message = Message::find($this->messageId);
            if (!$message) {
                Log::error('Message not found for ChatEmbeddingJob', [
                    'message_id' => $this->messageId,
                ]);
                return;
            }

            // Update message status to processing
            $previousStatus = $message->status;
            $message->update([
                'status' => MessageStatus::PROCESSING,
                'metadata' => array_merge($message->metadata ?? [], [
                    'embedding_started_at' => now()->toISOString(),
                ]),
            ]);

            // Broadcast status change
            broadcast(new \Modules\ChatBot\Events\MessageStatusChanged(
                $message,
                $previousStatus,
                MessageStatus::PROCESSING
            ));

            // Send request to Python service
            $this->sendToPythonService($message);

        } catch (Exception $e) {
            Log::error('ChatEmbeddingJob failed', [
                'message_id' => $this->messageId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Update message status to failed
            if (isset($message)) {
                $message->update([
                    'status' => MessageStatus::FAILED,
                    'metadata' => array_merge($message->metadata ?? [], [
                        'error' => $e->getMessage(),
                        'failed_at' => now()->toISOString(),
                    ]),
                ]);
            }

            throw $e;
        }
    }

    /**
     * Send request to Python embedding service.
     */
    private function sendToPythonService(Message $message): void
    {
        $pythonServiceUrl = config('chatbot.python.embedding_url');
        $webhookUrl = config('chatbot.webhook.url');
        $webhookSecret = config('chatbot.webhook.secret');

        if (!$pythonServiceUrl) {
            throw new Exception('Python embedding service URL not configured');
        }

        if (!$webhookUrl) {
            throw new Exception('Webhook URL not configured');
        }

        $conversation = $message->conversation;
        $bot = $conversation->bot;

        $payload = [
            // Core identifiers
            'message_id' => $this->messageId,
            'question' => $this->question,
            'conversation_id' => $this->conversationId,
            'bot_id' => $this->botId,

            // Webhook configuration
            'webhook_url' => $webhookUrl,
            'webhook_secret' => $webhookSecret,

            // Context information for better embedding
            'context' => [
                'conversation_history' => $this->getConversationHistory($conversation),
                'bot_config' => $this->getBotConfig($bot),
                'user_preferences' => $this->getUserPreferences($conversation->user),
                'knowledge_base' => $this->getKnowledgeBaseInfo($bot),
            ],

            // Processing options
            'options' => array_merge([
                'max_context_length' => 4000,
                'similarity_threshold' => 0.7,
                'max_results' => 10,
                'include_metadata' => true,
                'language' => 'vi', // Vietnamese
                'embedding_model' => 'text-embedding-ada-002',
            ], $this->options ?? []),

            // Request metadata
            'metadata' => array_merge([
                'timestamp' => now()->toISOString(),
                'request_id' => \Str::uuid()->toString(),
                'priority' => 'normal',
                'source' => 'laravel_chatbot',
                'version' => '1.0',
                'user_id' => $conversation->user_id,
            ], $this->metadata ?? []),
        ];

        Log::info('Sending request to Python service', [
            'url' => $pythonServiceUrl,
            'payload' => array_except($payload, ['webhook_secret']),
        ]);

        $response = Http::timeout(30)
            ->retry(2, 1000)
            ->post($pythonServiceUrl, $payload);

        if (!$response->successful()) {
            throw new Exception(
                "Python service request failed: {$response->status()} - {$response->body()}"
            );
        }

        $responseData = $response->json();

        Log::info('Python service response received', [
            'message_id' => $this->messageId,
            'response' => $responseData,
        ]);

        // Update message with Python service response
        $message->update([
            'metadata' => array_merge($message->metadata ?? [], [
                'python_request_sent_at' => now()->toISOString(),
                'python_response' => $responseData,
            ]),
        ]);
    }

    /**
     * Get conversation history for context.
     */
    private function getConversationHistory(Conversation $conversation): array
    {
        return $conversation->messages()
            ->where('status', MessageStatus::COMPLETED)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get(['role', 'content', 'created_at'])
            ->map(function ($message) {
                return [
                    'role' => $message->role->value,
                    'content' => $message->content,
                    'timestamp' => $message->created_at->toISOString(),
                ];
            })
            ->toArray();
    }

    /**
     * Get bot configuration for context.
     */
    private function getBotConfig(Bot $bot): array
    {
        return [
            'name' => $bot->name,
            'description' => $bot->description,
            'instructions' => $bot->instructions,
            'model' => $bot->model,
            'temperature' => $bot->temperature,
            'max_tokens' => $bot->max_tokens,
            'knowledge_base_enabled' => !empty($bot->knowledge_base_id),
        ];
    }

    /**
     * Get user preferences for context.
     */
    private function getUserPreferences($user): array
    {
        return [
            'language' => $user->language ?? 'vi',
            'timezone' => $user->timezone ?? 'Asia/Ho_Chi_Minh',
            'preferences' => $user->preferences ?? [],
        ];
    }

    /**
     * Get knowledge base information.
     */
    private function getKnowledgeBaseInfo(Bot $bot): array
    {
        if (!$bot->knowledge_base_id) {
            return [];
        }

        return [
            'id' => $bot->knowledge_base_id,
            'name' => $bot->knowledgeBase->name ?? null,
            'description' => $bot->knowledgeBase->description ?? null,
            'document_count' => $bot->knowledgeBase->documents()->count() ?? 0,
        ];
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('ChatEmbeddingJob permanently failed', [
            'message_id' => $this->messageId,
            'conversation_id' => $this->conversationId,
            'bot_id' => $this->botId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Update message status to failed
        $message = Message::find($this->messageId);
        if ($message) {
            $message->update([
                'status' => MessageStatus::FAILED,
                'metadata' => array_merge($message->metadata ?? [], [
                    'permanently_failed_at' => now()->toISOString(),
                    'final_error' => $exception->getMessage(),
                    'total_attempts' => $this->attempts(),
                ]),
            ]);
        }
    }
}
