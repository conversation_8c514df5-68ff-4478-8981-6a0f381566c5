<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Modules\ChatBot\Database\Factories\BotFactory;
use Modules\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Enums\BotVisibility;
use Modules\ChatBot\Enums\BotType;
use Modules\ChatBot\Enums\ToolCallingMode;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelTool;
use Modules\User\Models\User;

class Bot extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'name',
        'logo',
        'description',
        'owner_id',
        'owner_type',
        'model_ai_id',
        'system_prompt',
        'greeting_message',
        'starter_messages',
        'closing_message',
        'parameters',
        'tool_calling_mode',
        'status',
        'visibility',
        'bot_type',
        'is_shareable',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'starter_messages' => 'array',
        'closing_message' => 'array',
        'parameters' => 'array',
        'metadata' => 'array',
        'status' => BotStatus::class,
        'visibility' => BotVisibility::class,
        'bot_type' => BotType::class,
        'tool_calling_mode' => ToolCallingMode::class,
        'is_shareable' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Generate UUID on creation
        static::creating(function ($bot) {
            if (empty($bot->uuid)) {
                $bot->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the owner of the bot (polymorphic relationship).
     */
    public function owner(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the AI model associated with the bot.
     */
    public function aiModel(): BelongsTo
    {
        return $this->belongsTo(ModelAI::class, 'model_ai_id');
    }

    /**
     * Get all conversations for this bot.
     */
    public function conversations(): HasMany
    {
        return $this->hasMany(Conversation::class);
    }

    /**
     * Get active conversations for this bot.
     */
    public function activeConversations(): HasMany
    {
        return $this->hasMany(Conversation::class)->where('status', 'active');
    }

    /**
     * Get knowledge bases associated with this bot.
     */
    public function knowledgeBases(): BelongsToMany
    {
        return $this->belongsToMany(KnowledgeBase::class, 'bot_knowledge_bases');
    }

    /**
     * Get ready knowledge bases for this bot.
     */
    public function readyKnowledgeBases(): BelongsToMany
    {
        return $this->knowledgeBases()->where('status', 'ready');
    }

    /**
     * Get all shares for this bot (using new table structure).
     */
    public function shares(): HasMany
    {
        return $this->hasMany(BotShare::class);
    }

    /**
     * Get all share links for this bot.
     */
    public function shareLinks(): HasMany
    {
        return $this->hasMany(BotShareLink::class);
    }

    /**
     * Get active share links for this bot.
     */
    public function activeShareLinks(): HasMany
    {
        return $this->hasMany(BotShareLink::class)->active();
    }

    /**
     * Get users who have access to this bot through shares.
     */
    public function sharedUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'bot_shares', 'bot_id', 'user_id')
                    ->withTimestamps();
    }

    /**
     * Legacy method for backward compatibility.
     */
    public function userShares(): HasMany
    {
        return $this->shares();
    }

    /**
     * Get available tools for this bot through AI model.
     */
    public function availableTools()
    {
        if (!$this->aiModel) {
            return collect();
        }

        return $this->aiModel->tools()
                            ->wherePivot('is_enabled', true)
                            ->orderByPivot('priority', 'desc');
    }

    /**
     * Scope a query to only include active bots.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', BotStatus::Active);
    }

    /**
     * Scope a query to only include publicly visible bots.
     */
    public function scopePublic(Builder $query): Builder
    {
        return $query->whereIn('status', BotStatus::publicStatuses());
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, BotStatus|string $status): Builder
    {
        if ($status instanceof BotStatus) {
            $status = $status->value;
        }

        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by owner.
     */
    public function scopeOwnedBy(Builder $query, string $ownerType, int $ownerId): Builder
    {
        return $query->where('owner_type', $ownerType)
                    ->where('owner_id', $ownerId);
    }

    /**
     * Scope a query to only include public bots.
     */
    public function scopePublicVisibility(Builder $query): Builder
    {
        return $query->where('visibility', BotVisibility::PUBLIC);
    }

    /**
     * Scope a query to only include private bots.
     */
    public function scopePrivateVisibility(Builder $query): Builder
    {
        return $query->where('visibility', BotVisibility::PRIVATE);
    }

    /**
     * Scope a query to only include personal bots.
     */
    public function scopePersonal(Builder $query): Builder
    {
        return $query->where('bot_type', BotType::PERSONAL);
    }

    /**
     * Scope a query to only include organization bots.
     */
    public function scopeOrganization(Builder $query): Builder
    {
        return $query->where('bot_type', BotType::ORGANIZATION);
    }

    /**
     * Scope a query to only include shareable bots.
     */
    public function scopeShareable(Builder $query): Builder
    {
        return $query->where('is_shareable', true);
    }

    /**
     * Scope a query to include bots accessible by a user.
     */
    public function scopeAccessibleBy(Builder $query, int $userId): Builder
    {
        return $query->where(function ($q) use ($userId) {
            // Own bots
            $q->where(function ($subQ) use ($userId) {
                $subQ->where('owner_id', $userId)
                    ->where('owner_type', get_class(auth()->user()));
            })
            // Public bots
            ->orWhere('visibility', BotVisibility::PUBLIC)
            // Shared bots
            ->orWhereHas('shares', function ($shareQ) use ($userId) {
                $shareQ->where('user_id', $userId)
                    ->where('status', 'active');
            })
            // Bots from organizations where user is a member
            ->orWhere(function ($orgQ) use ($userId) {
                $orgQ->where('owner_type', 'Modules\\Organization\\Models\\Organization')
                    ->whereHas('owner', function ($memberQ) use ($userId) {
                        $memberQ->whereHas('members', function ($memberQuery) use ($userId) {
                            $memberQuery->where('user_id', $userId);
                        });
                    });
            });
        });
    }

    /**
     * Check if the bot is active.
     */
    public function isActive(): bool
    {
        return $this->status === BotStatus::Active;
    }

    /**
     * Check if the bot is editable.
     */
    public function isEditable(): bool
    {
        return $this->status->isEditable();
    }

    /**
     * Get the bot's display name.
     */
    public function getDisplayName(): string
    {
        return $this->name;
    }

    /**
     * Get the bot's status label.
     */
    public function getStatusLabel(): string
    {
        return $this->status->label();
    }

    /**
     * Get the bot's status color.
     */
    public function getStatusColor(): string
    {
        return $this->status->color();
    }

    /**
     * Get the tool calling mode label.
     */
    public function getToolCallingModeLabel(): string
    {
        return $this->tool_calling_mode->label();
    }

    /**
     * Get the tool calling mode description.
     */
    public function getToolCallingModeDescription(): string
    {
        return $this->tool_calling_mode->description();
    }

    /**
     * Get a specific parameter value.
     */
    public function getParameter(string $key, mixed $default = null): mixed
    {
        return data_get($this->parameters, $key, $default);
    }

    /**
     * Set a specific parameter value.
     */
    public function setParameter(string $key, mixed $value): void
    {
        $parameters = $this->parameters ?? [];
        data_set($parameters, $key, $value);
        $this->parameters = $parameters;
    }

    /**
     * Get a specific metadata value.
     */
    public function getMetadata(string $key, mixed $default = null): mixed
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * Set a specific metadata value.
     */
    public function setMetadata(string $key, mixed $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
    }

    /**
     * Check if the bot is public.
     */
    public function isPublic(): bool
    {
        return $this->visibility === BotVisibility::PUBLIC;
    }

    /**
     * Check if the bot is private.
     */
    public function isPrivate(): bool
    {
        return $this->visibility === BotVisibility::PRIVATE;
    }

    /**
     * Check if the bot is personal type.
     */
    public function isPersonal(): bool
    {
        return $this->bot_type === BotType::PERSONAL;
    }

    /**
     * Check if the bot is organization type.
     */
    public function isOrganization(): bool
    {
        return $this->bot_type === BotType::ORGANIZATION;
    }

    /**
     * Check if the bot can be shared.
     */
    public function canBeShared(): bool
    {
        return $this->is_shareable &&
               $this->isActive() &&
               $this->isPersonal(); // Only personal bots can be shared
    }

    /**
     * Check if a user can access this bot.
     */
    public function canBeAccessedBy(int $userId): bool
    {
        // Owner can always access
        if ($this->owner_id === $userId) {
            return true;
        }

        // Public bots can be accessed by anyone
        if ($this->isPublic()) {
            return true;
        }

        // Check if user has share
        return $this->shares()
                   ->where('user_id', $userId)
                   ->exists();
    }

    /**
     * Share the bot with a user for a specific shareable entity.
     */
    public function shareWith(int $userId, int $shareableId, string $shareableType): ?BotShare
    {
        if (!$this->canBeShared()) {
            return null;
        }

        // Create or get existing share
        return \Modules\ChatBot\Models\BotShare::createOrGet($this->id, $userId, $shareableId, $shareableType);
    }

    /**
     * Legacy method: Share the bot with a user (using User as shareable entity).
     */
    public function shareWithUser(int $userId): ?BotShare
    {
        return $this->shareWith($userId, $userId, User::class);
    }

    /**
     * Unshare the bot from a user for a specific shareable entity.
     */
    public function unshareFrom(int $userId, int $shareableId, string $shareableType): bool
    {
        return $this->shares()
                   ->where('user_id', $userId)
                   ->where('shareable_id', $shareableId)
                   ->where('shareable_type', $shareableType)
                   ->delete() > 0;
    }

    /**
     * Legacy method: Unshare the bot from a user (using User as shareable entity).
     */
    public function unshareFromUser(int $userId): bool
    {
        return $this->unshareFrom($userId, $userId, User::class);
    }

    /**
     * Get user's permission level for this bot.
     */
    public function getUserPermission(int $userId): ?string
    {
        // Owner has admin permission
        if ($this->owner_id === $userId) {
            return 'admin';
        }

        // Check if user has share (simplified permission model)
        $hasShare = $this->shares()
                        ->where('user_id', $userId)
                        ->exists();

        return $hasShare ? 'read' : null;
    }

    /**
     * Check if user can perform action on this bot.
     */
    public function userCan(int $userId, string $action): bool
    {
        $permission = $this->getUserPermission($userId);

        if (!$permission) {
            return false;
        }

        return match ($action) {
            'read', 'view', 'use' => in_array($permission, ['read', 'admin']),
            'write', 'edit', 'admin', 'delete', 'share' => $permission === 'admin',
            default => false,
        };
    }

    /**
     * Get total number of shares for this bot.
     */
    public function getTotalSharesCount(): int
    {
        return $this->shares()->count();
    }

    /**
     * Get list of users this bot is shared with.
     */
    public function getSharedWithUsers(): Collection
    {
        return $this->sharedUsers()->get();
    }

    /**
     * Check if bot is shared with a specific user.
     */
    public function isSharedWith(int $userId): bool
    {
        return $this->shares()
                   ->where('user_id', $userId)
                   ->exists();
    }

    /**
     * Create a share link for this bot.
     */
    public function createShareLink(int $shareableId, string $shareableType, ?\DateTimeInterface $expiresAt = null): ?BotShareLink
    {
        if (!$this->canBeShared()) {
            return null;
        }

        return \Modules\ChatBot\Models\BotShareLink::createForBot($this->id, $shareableId, $shareableType, $expiresAt);
    }

    /**
     * Get active share links count.
     */
    public function getActiveShareLinksCount(): int
    {
        return $this->activeShareLinks()->count();
    }

    /**
     * Attach a knowledge base to this bot.
     */
    public function attachKnowledgeBase(int $knowledgeBaseId): void
    {
        $this->knowledgeBases()->syncWithoutDetaching([$knowledgeBaseId]);
    }

    /**
     * Detach a knowledge base from this bot.
     */
    public function detachKnowledgeBase(int $knowledgeBaseId): void
    {
        $this->knowledgeBases()->detach($knowledgeBaseId);
    }

    /**
     * Check if bot has a specific knowledge base.
     */
    public function hasKnowledgeBase(int $knowledgeBaseId): bool
    {
        return $this->knowledgeBases()->where('knowledge_base_id', $knowledgeBaseId)->exists();
    }

    /**
     * Get knowledge base statistics for this bot.
     */
    public function getKnowledgeBaseStats(): array
    {
        return [
            'total_knowledge_bases' => $this->knowledgeBases()->count(),
            'ready_knowledge_bases' => $this->readyKnowledgeBases()->count(),
            'pending_knowledge_bases' => $this->knowledgeBases()->where('status', 'pending')->count(),
            'processing_knowledge_bases' => $this->knowledgeBases()->where('status', 'processing')->count(),
            'error_knowledge_bases' => $this->knowledgeBases()->where('status', 'error')->count(),
        ];
    }

    /**
     * Get the logo URL for this bot.
     */
    public function getLogoUrl(): ?string
    {
        if (!$this->logo) {
            return null;
        }

        // If logo is already a full URL, return as is
        if (filter_var($this->logo, FILTER_VALIDATE_URL)) {
            return $this->logo;
        }

        // If logo is a storage path, generate URL
        return \Storage::url($this->logo);
    }

    /**
     * Get the logo path for storage operations.
     */
    public function getLogoPath(): ?string
    {
        return $this->logo;
    }

    /**
     * Check if bot has a logo.
     */
    public function hasLogo(): bool
    {
        return !empty($this->logo);
    }

    /**
     * Update bot logo.
     */
    public function updateLogo(?string $logoPath): bool
    {
        // Delete old logo if exists and is a file path
        if ($this->logo && !filter_var($this->logo, FILTER_VALIDATE_URL)) {
            \Storage::delete($this->logo);
        }

        return $this->update(['logo' => $logoPath]);
    }

    /**
     * Delete bot logo.
     */
    public function deleteLogo(): bool
    {
        if ($this->logo && !filter_var($this->logo, FILTER_VALIDATE_URL)) {
            \Storage::delete($this->logo);
        }

        return $this->update(['logo' => null]);
    }

    /**
     * Check if bot has a greeting message.
     */
    public function hasGreetingMessage(): bool
    {
        return !empty($this->greeting_message);
    }

    /**
     * Check if bot has conversation starters.
     */
    public function hasConversationStarters(): bool
    {
        return !empty($this->starter_messages) && is_array($this->starter_messages);
    }

    /**
     * Check if bot has a closing message.
     */
    public function hasClosingMessage(): bool
    {
        return !empty($this->closing_message);
    }

    /**
     * Get conversation starters as array.
     */
    public function getConversationStarters(): array
    {
        return $this->starter_messages ?? [];
    }

    /**
     * Add a conversation starter.
     */
    public function addConversationStarter(string $starter): bool
    {
        $starters = $this->getConversationStarters();
        $starters[] = $starter;

        return $this->update(['starter_messages' => $starters]);
    }

    /**
     * Remove a conversation starter by index.
     */
    public function removeConversationStarter(int $index): bool
    {
        $starters = $this->getConversationStarters();

        if (isset($starters[$index])) {
            unset($starters[$index]);
            $starters = array_values($starters); // Re-index array

            return $this->update(['starter_messages' => $starters]);
        }

        return false;
    }

    /**
     * Get the greeting message or default.
     */
    public function getGreetingMessage(): string
    {
        return $this->greeting_message ?? "Xin chào! Tôi là {$this->name}. Tôi có thể giúp gì cho bạn?";
    }

    /**
     * Get the closing message or default.
     */
    public function getClosingMessage(): string
    {
        return $this->closing_message ?? "Cảm ơn bạn đã trò chuyện với tôi. Hẹn gặp lại!";
    }

    /**
     * Get sharing statistics for this bot.
     */
    public function getSharingStats(): array
    {
        return [
            'total_shares' => $this->getTotalSharesCount(),
            'shared_users_count' => $this->sharedUsers()->count(),
            'total_share_links' => $this->shareLinks()->count(),
            'active_share_links' => $this->getActiveShareLinksCount(),
            'is_shareable' => $this->canBeShared(),
            'sharing_enabled' => $this->is_shareable,
            'bot_type' => $this->bot_type->value,
            'visibility' => $this->visibility->value,
        ];
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): BotFactory
    {
        return BotFactory::new();
    }
}
