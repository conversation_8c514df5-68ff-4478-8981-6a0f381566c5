<?php

namespace Modules\ChatBot\Facades;

use Illuminate\Support\Facades\Facade;
use Mo<PERSON>les\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * Streaming Facade
 *
 * Provides convenient access to streaming functionality through a static interface.
 *
 * @method static StreamedResponse generateStreamingResponse(Conversation $conversation, array $options = [])
 * @method static bool isStreamingSupported(Bot $bot)
 *
 * @see \Modules\ChatBot\Services\StreamingService
 */
class StreamingFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'chatbot.streaming';
    }
}
