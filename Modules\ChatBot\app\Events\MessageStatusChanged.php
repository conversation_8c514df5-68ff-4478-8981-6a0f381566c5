<?php

namespace Modules\ChatBot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageStatus;

class MessageStatusChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Message $message,
        public MessageStatus $previousStatus,
        public MessageStatus $newStatus
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('conversation.' . $this->message->conversation_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.status.changed';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'message_id' => $this->message->id,
            'message_uuid' => $this->message->uuid,
            'conversation_id' => $this->message->conversation_id,
            'previous_status' => $this->previousStatus->value,
            'new_status' => $this->newStatus->value,
            'content' => $this->newStatus === MessageStatus::COMPLETED ? $this->message->content : null,
            'metadata' => $this->message->metadata,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Determine if this event should broadcast.
     */
    public function broadcastWhen(): bool
    {
        // Only broadcast for significant status changes
        return in_array($this->newStatus, [
            MessageStatus::PROCESSING,
            MessageStatus::COMPLETED,
            MessageStatus::FAILED,
        ]);
    }
}
