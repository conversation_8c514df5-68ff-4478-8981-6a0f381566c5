<?php

namespace Modules\ChatBot\Http\Controllers;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Services\EmbeddingContextService;
use Modules\ChatBot\Services\ChatBroadcastService;
use Modules\ChatBot\Facades\AIFacade;

class ChatWebhookController extends Controller
{
    public function __construct(
        private EmbeddingContextService $embeddingContextService,
        private ChatBroadcastService $broadcastService
    ) {}

    /**
     * Handle webhook from Python embedding service.
     */
    public function handleEmbeddingResult(Request $request): JsonResponse
    {
        try {
            Log::info('Embedding webhook received', [
                'payload' => $request->all(),
                'headers' => $request->headers->all(),
            ]);

            // Validate webhook signature
            if (!$this->validateWebhookSignature($request)) {
                Log::warning('Invalid webhook signature', [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Invalid signature',
                ], 401);
            }

            // Validate required fields
            $validated = $request->validate([
                'message_id' => 'required|integer',
                'conversation_id' => 'required|integer',
                'bot_id' => 'required|integer',
                'context_data' => 'required|array',
                'relevant_documents' => 'array',
                'metadata' => 'array',
            ]);

            $messageId = $validated['message_id'];
            $message = Message::find($messageId);

            if (!$message) {
                Log::error('Message not found for webhook', [
                    'message_id' => $messageId,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Message not found',
                ], 404);
            }

            // Process embedding results
            $this->processEmbeddingResults($message, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully',
                'message_id' => $messageId,
            ]);

        } catch (Exception $e) {
            Log::error('Webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Process embedding results and generate AI response.
     */
    private function processEmbeddingResults(Message $message, array $data): void
    {
        try {
            // Update message with embedding results
            $message->update([
                'status' => MessageStatus::PROCESSING,
                'metadata' => array_merge($message->metadata ?? [], [
                    'embedding_completed_at' => now()->toISOString(),
                    'context_data' => $data['context_data'],
                    'relevant_documents' => $data['relevant_documents'] ?? [],
                    'python_metadata' => $data['metadata'] ?? [],
                ]),
            ]);

            Log::info('Processing embedding results', [
                'message_id' => $message->id,
                'context_data_count' => count($data['context_data']),
                'relevant_documents_count' => count($data['relevant_documents'] ?? []),
            ]);

            // Build enhanced context with embedding results
            $enhancedContext = $this->embeddingContextService->buildEnhancedContext(
                $message->conversation,
                $data['context_data'],
                $data['relevant_documents'] ?? []
            );

            // Generate AI response with enhanced context
            $this->generateAIResponseWithContext($message, $enhancedContext);

        } catch (Exception $e) {
            Log::error('Failed to process embedding results', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);

            $message->update([
                'status' => MessageStatus::FAILED,
                'metadata' => array_merge($message->metadata ?? [], [
                    'processing_error' => $e->getMessage(),
                    'failed_at' => now()->toISOString(),
                ]),
            ]);

            throw $e;
        }
    }

    /**
     * Generate AI response with enhanced context.
     */
    private function generateAIResponseWithContext(Message $message, array $enhancedContext): void
    {
        try {
            $conversation = $message->conversation;
            $bot = $conversation->bot;

            Log::info('Generating AI response with enhanced context', [
                'message_id' => $message->id,
                'bot_id' => $bot->id,
                'context_messages_count' => count($enhancedContext),
            ]);

            // Generate AI response
            $response = AIFacade::generateResponse(
                $bot,
                $enhancedContext,
                ['enhanced_context' => true]
            );

            // Update message with AI response
            $message->update([
                'content' => $response['content'],
                'status' => MessageStatus::COMPLETED,
                'tool_calls' => $response['tool_calls'] ?? null,
                'prompt_tokens' => $response['usage']['prompt_tokens'] ?? null,
                'completion_tokens' => $response['usage']['completion_tokens'] ?? null,
                'total_tokens' => $response['usage']['total_tokens'] ?? null,
                'cost' => $response['cost'] ?? null,
                'response_time_ms' => $response['response_time_ms'] ?? null,
                'completed_at' => now(),
                'metadata' => array_merge($message->metadata ?? [], [
                    'ai_response_generated_at' => now()->toISOString(),
                    'enhanced_context_used' => true,
                ]),
            ]);

            // Broadcast response via socket
            $this->broadcastService->broadcastMessageCompletion($message);

            Log::info('AI response generated successfully', [
                'message_id' => $message->id,
                'response_length' => strlen($response['content']),
                'tokens_used' => $response['usage']['total_tokens'] ?? 0,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to generate AI response', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);

            $message->update([
                'status' => MessageStatus::FAILED,
                'metadata' => array_merge($message->metadata ?? [], [
                    'ai_generation_error' => $e->getMessage(),
                    'failed_at' => now()->toISOString(),
                ]),
            ]);

            throw $e;
        }
    }



    /**
     * Validate webhook signature.
     */
    private function validateWebhookSignature(Request $request): bool
    {
        $secret = config('chatbot.webhook.secret');
        
        if (!$secret) {
            // If no secret configured, skip validation
            return true;
        }

        $signature = $request->header('X-Webhook-Signature');
        if (!$signature) {
            return false;
        }

        $payload = $request->getContent();
        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        return hash_equals($expectedSignature, $signature);
    }
}
