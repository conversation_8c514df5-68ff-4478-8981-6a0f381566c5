<?php

namespace Modules\ChatBot\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Modules\ChatBot\Models\BotShare;
use Modules\ChatBot\Models\BotShareLink;
use <PERSON>dules\ChatBot\Models\KnowledgeBase;
use <PERSON><PERSON><PERSON>\ChatBot\Observers\BotShareLinkObserver;
use Modules\ChatBot\Observers\BotShareObserver;
use Modules\ChatBot\Observers\KnowledgeBaseObserver;
use Modules\ChatBot\Services\AIService;
use Modules\ChatBot\Services\BotService;
use Modules\ChatBot\Services\ChatBroadcastService;
use Modules\ChatBot\Services\ConversationService;
use Modules\ChatBot\Services\EmbeddingContextService;
use Modules\ChatBot\Services\MessageService;
use Modules\ChatBot\Services\StreamingService;
use Nwidart\Modules\Traits\PathNamespace;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;

class ChatBotServiceProvider extends ServiceProvider
{
    use PathNamespace;

    protected string $name = 'ChatBot';

    protected string $nameLower = 'chatbot';

    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerCommands();
        $this->registerCommandSchedules();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerObservers();
        $this->loadMigrationsFrom(module_path($this->name, 'database/migrations'));
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {
        $this->app->register(EventServiceProvider::class);
        $this->app->register(RouteServiceProvider::class);

        // Register services
        $this->registerServices();

        // Register facades
        $this->registerFacades();
    }

    /**
     * Register module services.
     */
    protected function registerServices(): void
    {
        // Register AIService for facade
        $this->app->singleton('ai.service', function ($app) {
            return new AIService();
        });

        // Register BotService as singleton
        $this->app->singleton('bot.service', function ($app) {
            return new BotService();
        });

        // Register ConversationService as singleton
        $this->app->singleton('conversation.service', function ($app) {
            return new ConversationService();
        });

        // Register MessageService as singleton
        $this->app->singleton('chatbot.message', function ($app) {
            return new MessageService();
        });

        // Register StreamingService as singleton
        $this->app->singleton('chatbot.streaming', function ($app) {
            return new StreamingService(
                $app->make(AIService::class),
                $app->make(MessageService::class)
            );
        });

        // Register EmbeddingContextService as singleton
        $this->app->singleton('chatbot.embedding.context', function ($app) {
            return new EmbeddingContextService();
        });

        // Register ChatBroadcastService as singleton
        $this->app->singleton('chatbot.broadcast', function ($app) {
            return new ChatBroadcastService();
        });
    }

    /**
     * Register module facades.
     */
    protected function registerFacades(): void
    {
        // Register facades (already bound in registerServices)
        // This method can be used for additional facade configurations if needed
    }

    /**
     * Register commands in the format of Command::class
     */
    protected function registerCommands(): void
    {
        // $this->commands([]);
    }

    /**
     * Register command Schedules.
     */
    protected function registerCommandSchedules(): void
    {
        // $this->app->booted(function () {
        //     $schedule = $this->app->make(Schedule::class);
        //     $schedule->command('inspire')->hourly();
        // });
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/' . $this->nameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->nameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->name, 'lang'), $this->nameLower);
            $this->loadJsonTranslationsFrom(module_path($this->name, 'lang'));
        }
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $configPath = module_path($this->name, config('modules.paths.generator.config.path'));

        if (is_dir($configPath)) {
            $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($configPath));

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $config = str_replace($configPath . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $config_key = str_replace([DIRECTORY_SEPARATOR, '.php'], ['.', ''], $config);
                    $segments = explode('.', $this->nameLower . '.' . $config_key);

                    // Remove duplicated adjacent segments
                    $normalized = [];
                    foreach ($segments as $segment) {
                        if (end($normalized) !== $segment) {
                            $normalized[] = $segment;
                        }
                    }

                    $key = ($config === 'config.php') ? $this->nameLower : implode('.', $normalized);

                    $this->publishes([$file->getPathname() => config_path($config)], 'config');
                    $this->merge_config_from($file->getPathname(), $key);
                }
            }
        }
    }

    /**
     * Merge config from the given path recursively.
     */
    protected function merge_config_from(string $path, string $key): void
    {
        $existing = config($key, []);
        $module_config = require $path;

        config([$key => array_replace_recursive($existing, $module_config)]);
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/' . $this->nameLower);
        $sourcePath = module_path($this->name, 'resources/views');

        $this->publishes([$sourcePath => $viewPath], ['views', $this->nameLower . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->nameLower);

        Blade::componentNamespace(config('modules.namespace') . '\\' . $this->name . '\\View\\Components', $this->nameLower);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            'bot.service',
            'conversation.service',
            'ai.service',
            'chatbot.message',
        ];
    }

    /**
     * Register model observers for cache invalidation.
     */
    protected function registerObservers(): void
    {
        BotShare::observe(BotShareObserver::class);
        BotShareLink::observe(BotShareLinkObserver::class);
        KnowledgeBase::observe(KnowledgeBaseObserver::class);
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->nameLower)) {
                $paths[] = $path . '/modules/' . $this->nameLower;
            }
        }

        return $paths;
    }
}
