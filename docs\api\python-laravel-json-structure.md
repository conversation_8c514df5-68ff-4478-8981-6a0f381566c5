# JSON Structure - Laravel ↔ Python Communication

## 1. Request từ Laravel đến Python (Embedding & Query)

### Endpoint: `POST /api/v1/embedding/query`

```json
{
  "message_id": 12345,
  "question": "<PERSON><PERSON> là gì và cách sử dụng như thế nào?",
  "conversation_id": 67890,
  "bot_id": 123,
  "webhook_url": "https://your-domain.com/api/v1/webhooks/embedding/result",
  "webhook_secret": "your-webhook-secret-key",
  
  "context": {
    "conversation_history": [
      {
        "role": "user",
        "content": "Tôi muốn học PHP framework",
        "timestamp": "2024-01-15T10:30:00Z"
      },
      {
        "role": "assistant", 
        "content": "Có nhiều PHP framework phổ biến như <PERSON>, Symfony...",
        "timestamp": "2024-01-15T10:30:15Z"
      }
    ],
    "bot_config": {
      "name": "<PERSON><PERSON> Expert Bot",
      "description": "Chuyên gia về Laravel framework",
      "instructions": "Trả lời chi tiết về Laravel với ví dụ code",
      "model": "gpt-4",
      "temperature": 0.7,
      "max_tokens": 2000,
      "knowledge_base_enabled": true
    },
    "user_preferences": {
      "language": "vi",
      "timezone": "Asia/Ho_Chi_Minh",
      "preferences": {
        "code_style": "detailed",
        "explanation_level": "intermediate"
      }
    },
    "knowledge_base": {
      "id": 456,
      "name": "Laravel Documentation",
      "description": "Official Laravel docs and tutorials",
      "document_count": 150
    }
  },
  
  "options": {
    "max_context_length": 4000,
    "similarity_threshold": 0.7,
    "max_results": 10,
    "include_metadata": true,
    "language": "vi",
    "embedding_model": "text-embedding-ada-002"
  },
  
  "metadata": {
    "timestamp": "2024-01-15T10:35:00Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "priority": "normal",
    "source": "laravel_chatbot",
    "version": "1.0",
    "user_id": 789
  }
}
```

## 2. Response từ Python về Laravel (Webhook)

### Endpoint: `POST /api/v1/webhooks/embedding/result`

```json
{
  "message_id": 12345,
  "conversation_id": 67890,
  "bot_id": 123,
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  
  "status": "success",
  "processing_time": 2.5,
  
  "context_data": [
    {
      "content": "Laravel là một PHP framework mã nguồn mở được phát triển bởi Taylor Otwell...",
      "source": "laravel_docs_introduction.md",
      "score": 0.95,
      "chunk_id": "chunk_001",
      "metadata": {
        "section": "Introduction",
        "last_updated": "2024-01-10T00:00:00Z"
      }
    },
    {
      "content": "Cài đặt Laravel thông qua Composer: composer create-project laravel/laravel example-app",
      "source": "laravel_docs_installation.md", 
      "score": 0.87,
      "chunk_id": "chunk_045",
      "metadata": {
        "section": "Installation",
        "last_updated": "2024-01-08T00:00:00Z"
      }
    }
  ],
  
  "relevant_documents": [
    {
      "title": "Laravel Documentation - Getting Started",
      "content": "Laravel is a web application framework with expressive, elegant syntax...",
      "url": "https://laravel.com/docs/getting-started",
      "score": 0.95,
      "document_id": "doc_001",
      "metadata": {
        "type": "documentation",
        "category": "framework",
        "tags": ["laravel", "php", "framework", "getting-started"]
      }
    },
    {
      "title": "Laravel Tutorial - Building Your First App",
      "content": "In this tutorial, we'll build a simple blog application using Laravel...",
      "url": "https://example.com/laravel-tutorial",
      "score": 0.82,
      "document_id": "doc_015",
      "metadata": {
        "type": "tutorial",
        "category": "beginner",
        "tags": ["laravel", "tutorial", "beginner"]
      }
    }
  ],
  
  "embedding_results": {
    "query_embedding": [0.1, 0.2, -0.3, ...], // Vector embedding của câu hỏi
    "total_documents_searched": 150,
    "documents_found": 25,
    "documents_returned": 10,
    "search_time_ms": 150
  },
  
  "metadata": {
    "timestamp": "2024-01-15T10:35:02Z",
    "processing_started_at": "2024-01-15T10:35:00Z",
    "processing_completed_at": "2024-01-15T10:35:02Z",
    "similarity_scores": [0.95, 0.87, 0.82, 0.78, 0.75],
    "model_used": "text-embedding-ada-002",
    "tokens_used": {
      "query_tokens": 15,
      "context_tokens": 3500
    },
    "filters_applied": {
      "language": "vi",
      "knowledge_base_id": 456,
      "minimum_score": 0.7
    }
  }
}
```

## 3. Error Response từ Python

```json
{
  "message_id": 12345,
  "conversation_id": 67890,
  "bot_id": 123,
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  
  "status": "error",
  "error": {
    "code": "EMBEDDING_FAILED",
    "message": "Failed to generate embedding for the query",
    "details": "OpenAI API rate limit exceeded",
    "timestamp": "2024-01-15T10:35:01Z"
  },
  
  "metadata": {
    "processing_time": 1.2,
    "retry_count": 3,
    "last_attempt_at": "2024-01-15T10:35:01Z"
  }
}
```

## 4. Webhook Security

### Headers Required:
- `X-Webhook-Signature`: SHA256 HMAC của request body
- `Content-Type`: `application/json`
- `User-Agent`: `Python-Embedding-Service/1.0`

### Signature Calculation:
```python
import hmac
import hashlib

def generate_signature(payload, secret):
    signature = hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    return f"sha256={signature}"
```

## 5. Status Codes

### Request to Python:
- `200`: Success - Request accepted and processing
- `400`: Bad Request - Invalid payload
- `401`: Unauthorized - Invalid API key
- `429`: Rate Limited - Too many requests
- `500`: Internal Error - Python service error

### Webhook Response:
- `200`: Success - Webhook processed successfully
- `400`: Bad Request - Invalid webhook payload
- `401`: Unauthorized - Invalid signature
- `422`: Validation Error - Missing required fields
- `500`: Internal Error - Laravel processing error
